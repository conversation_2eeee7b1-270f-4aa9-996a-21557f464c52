package com.ybmmarket20.view

import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.View
import android.widget.TextView
import androidx.activity.ComponentActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.isVisible
import androidx.lifecycle.SavedStateViewModelFactory
import androidx.lifecycle.ViewModelProvider
import com.ybmmarket20.R
import com.ybmmarket20.bean.ProductDetailBean
import com.ybmmarket20.bean.ProductDetailBeanWrapper.ShopInfo
import com.ybmmarket20.bean.product_detail.ReportPDButtonClick
import com.ybmmarket20.bean.product_detail.ReportPDExtendOuterBean
import com.ybmmarket20.common.JGTrackManager
import com.ybmmarket20.common.JgTrackBean
import com.ybmmarket20.common.jgReport
import com.ybmmarket20.common.jgTrackProductDetailNewBtnClick
import com.ybmmarket20.common.jgTrackProductDetailNewBtnExposure
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_RIGHT_NOW
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_SELECT_GOODS
import com.ybmmarket20.viewmodel.SPELL_GROUP_RECOMMEND_ADD_TO_CART
import com.ybmmarket20.viewmodel.SpellGroupRecommendGoodsViewModel
import com.ybmmarket20.viewmodel.viewstore.GlobalViewModelStore
import com.ybmmarket20.xyyreport.page.commodity.CommodityDetailReport
import kotlinx.android.synthetic.main.detail_operation_tool_recommend_goods.view.*

/**
 * 商祥随心拼底部工具栏
 */
class DetailOperationToolRecommendGoodsView(context: Context, attr: AttributeSet) :
    ConstraintLayout(context, attr) {

    private var mJumpCallback: (() -> Unit)? = null
    var jgTrackBean:JgTrackBean? = null
        set(value) {
            field = value
            rtv_spell_group_right_now?.btnExposure()
            rtv_spell_group_recommend_goods?.btnExposure()
        }

    var shopCode = ""
    var orgId = ""
    var mainGoodsSkuId = ""
    var isThirdCompany = 0
    var mainGoodsCount = ""
    var mainGoodsPId: String? = null
    var productDetail: ProductDetailBean? = null
        set(value) {
            setControlGoodsBtnStyle(value)
            field = value
        }
    var pdExtendOuterBean: ReportPDExtendOuterBean? = null
    var shopInfo: ShopInfo? = null
    var SPELL_GROUP_RECOMMEND_TYPE = 0 // 跳转类型
    var canAddToCart = false // 拼团商品可加购

    init {
        View.inflate(context, R.layout.detail_operation_tool_recommend_goods, this)
        rtv_spell_group_right_now.setOnClickListener {
            //立即参团
            SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_RIGHT_NOW
            registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_RIGHT_NOW)

            btnClick(rtv_spell_group_right_now?.text?.toString())
            CommodityDetailReport.bottomBtnClickRight(context, 1, rtv_spell_group_right_now.text.toString())
        }
        rtv_spell_group_recommend_goods.setOnClickListener {
            if (canAddToCart) {
                // 拼团商品可加购
                SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_ADD_TO_CART
                registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_ADD_TO_CART)
            } else {
                //随心拼
                SPELL_GROUP_RECOMMEND_TYPE = SPELL_GROUP_RECOMMEND_SELECT_GOODS
                registerRecommendGoodsJumpType(SPELL_GROUP_RECOMMEND_SELECT_GOODS)
            }

            btnClick(rtv_spell_group_recommend_goods?.text?.toString())
            CommodityDetailReport.bottomBtnClickRight(context, 2, rtv_spell_group_recommend_goods.text.toString())
        }
    }

    //按钮曝光
    private fun TextView.btnExposure(){
        if (this.isVisible){
            context.jgTrackProductDetailNewBtnExposure(
                    url = jgTrackBean?.url,
                    pageId = jgTrackBean?.pageId,
                    title  = jgTrackBean?.title,
                    btnName = this.text?.toString(),
                    btnDesc = if (jgTrackBean?.pageId == JGTrackManager.TrackProductDetail.PAGE_ID) "商详页" else "底部弹窗",
                    jgReferrer = jgTrackBean?.jgReferrer,
                    rank = jgTrackBean?.rank?:1,
                    productId =jgTrackBean?.productId?:"",
                    productType =jgTrackBean?.productType?:"",
                    operationId =jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                    operationRank =jgTrackBean?.mJgOperationPositionInfo?.operationRank?:1,
                    module =jgTrackBean?.module?:"",
                    navigation1 =jgTrackBean?.navigation_1?:"",
                    navigation2 = jgTrackBean?.navigation_2?:""
                                                      )
        }
    }

    private fun btnClick(btnNameText:String?){

        var mProductId = 0
        try {
            mProductId = jgTrackBean?.productId?.toInt()?:0
        }catch (e:Exception){
            e.printStackTrace()
        }

        context.jgTrackProductDetailNewBtnClick(
                url = jgTrackBean?.url,
                pageId = jgTrackBean?.pageId,
                title  = jgTrackBean?.title,
                btnName = btnNameText?:"",
                btnDesc = "商详页",
                jgReferrer = jgTrackBean?.jgReferrer,
                rank = jgTrackBean?.rank?:1,//
                productId =jgTrackBean?.productId?:"",
                productType =jgTrackBean?.productType?:"",
                operationId =jgTrackBean?.mJgOperationPositionInfo?.operationId?:"",
                operationRank =jgTrackBean?.mJgOperationPositionInfo?.operationRank?:1,
                module =jgTrackBean?.module?:"",
                navigation1 =jgTrackBean?.navigation_1?:"",
                navigation2 = jgTrackBean?.navigation_2?:"")
        // 移除极光埋点 - action_product_button_click (保留搜索页面)
        /*
        context.jgReport(
            ReportPDButtonClick().apply {
                url = jgTrackBean?.url ?: ""
                title = jgTrackBean?.title ?: ""
                referrer = jgTrackBean?.jgReferrer ?: ""
                accountId = SpUtil.getAccountId()
                merchantId = SpUtil.getMerchantid()
                outerBean = pdExtendOuterBean
                productId = mProductId
                productName = productDetail?.showName ?: ""
                productFirst = productDetail?.categoryFirstId ?: ""
                productPrice = productDetail?.jgPrice
                productType = productDetail?.productType?.toString()
                productShopCode = productDetail?.shopCode ?: ""
                productShopName = shopInfo?.shopName ?: ""
                productActivityType = productDetail?.productActivityType
                btnName = btnNameText ?: ""
                btnDesc = "商详页"
                direct = "2"
            }

        )
        */
    }

    /**
     * 设置控销品按钮样式
     */
    private fun setControlGoodsBtnStyle(pd: ProductDetailBean?) {
        if (pd?.isControlGoods == true) {
            rtv_spell_group_right_now.isEnabled = false
            rtv_spell_group_recommend_goods.isEnabled = false
            rtv_spell_group_right_now.setTextColor(Color.parseColor("#AAACB9"))
            rtv_spell_group_right_now.setStrokeColor(Color.parseColor("#AAACB9"))
            rtv_spell_group_recommend_goods.setTextColor(Color.parseColor("#FFFFFF"))
            rtv_spell_group_recommend_goods.setBackgroundColor(Color.parseColor("#AAACB9"))
        }
    }

    /**
     * 设置商品可加购物车样式
     */
    fun setCanAddToCart(pd: ProductDetailBean?) {
        if (pd?.canAddToCart == true) {
            rtv_spell_group_recommend_goods.text = "加入购物车"
            tvSuiXinPinBubble.visibility = View.GONE
        }
    }

    /**
     * 注册随心拼跳转类型
     */
    fun registerRecommendGoodsJumpType(jumpType: Int, isShowPopupWindow: Boolean = true) {
        if (context is ComponentActivity) {
            if (isShowPopupWindow) {
                mJumpCallback?.invoke()
            }
            val viewModel: SpellGroupRecommendGoodsViewModel = ViewModelProvider(GlobalViewModelStore.get().getGlobalViewModelStore(), SavedStateViewModelFactory(
                (context as ComponentActivity).application,
                context as ComponentActivity
            )).get(SpellGroupRecommendGoodsViewModel::class.java)
            viewModel.shopCode = shopCode
            viewModel.orgId = orgId
            viewModel.mainGoodsSkuId = mainGoodsSkuId
            viewModel.isThirdCompany = isThirdCompany
            viewModel.mainGoodsCount = mainGoodsCount
            viewModel.mainGoodsPId = mainGoodsPId
            viewModel.registerJumpType(jumpType)
        }
    }

    fun setSuiXinPinBubble(text: String?) {
        tvSuiXinPinBubble.visibility = if (text.isNullOrEmpty()) View.GONE else View.VISIBLE
        tvSuiXinPinBubble.text = text
    }

    /**
     * 点击跳转监听
     */
    fun setOnJumpClickCallback(jumpCallback: (() -> Unit)?) {
        mJumpCallback = jumpCallback
    }

    /**
     * 设置活动类型，拼团或批购包邮
     */
    fun setActPtType(isSpellGroup: Boolean, isWholeSale: Boolean) {
        if (isSpellGroup) {
            rtv_spell_group_right_now.text = "立即参团"
        }
        if (isWholeSale) {
            rtv_spell_group_right_now.text = "去抢购"
        }
    }
}